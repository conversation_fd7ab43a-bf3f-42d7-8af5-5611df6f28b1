{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "v5.15.1", "@strapi/plugin-users-permissions": "v5.15.1", "@strapi/provider-email-nodemailer": "^v5.15.1", "@strapi/strapi": "v5.15.1", "pg": "^8.16.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "290a2e299d9bc6f90b2fdf1cd3540762bf435f61cf23e226a030ba9b4ef07598"}}